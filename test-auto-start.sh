#!/bin/bash

# Test script for frontend auto-start functionality
# This script tests both development and production scenarios

echo "🧪 Testing Frontend Auto-Start Functionality"
echo "=============================================="

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for a port to be available
wait_for_port() {
    local port=$1
    local timeout=${2:-30}
    local count=0
    
    echo "⏳ Waiting for port $port to be available..."
    while [ $count -lt $timeout ]; do
        if check_port $port; then
            echo "✅ Port $port is now available"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    echo "❌ Timeout waiting for port $port"
    return 1
}

# Function to test development mode
test_development() {
    echo ""
    echo "🔧 Testing Development Mode"
    echo "----------------------------"
    
    # Check if required files exist
    if [ ! -f "WHO.MALARIA.Web/malaria-client/dev.sh" ]; then
        echo "❌ dev.sh script not found"
        return 1
    fi
    
    if [ ! -f "WHO.MALARIA.Web/malaria-client/vite.config.js" ]; then
        echo "❌ vite.config.js not found"
        return 1
    fi
    
    echo "✅ Required files exist"
    
    # Start the application in development mode
    echo "🚀 Starting application in development mode..."
    cd WHO.MALARIA.Web
    ASPNETCORE_ENVIRONMENT=Development dotnet run &
    BACKEND_PID=$!
    cd ..
    
    # Wait for both backend and frontend to start
    echo "⏳ Waiting for services to start..."
    sleep 10
    
    # Check if backend is running (port 5001)
    if wait_for_port 5001 30; then
        echo "✅ Backend started successfully"
    else
        echo "❌ Backend failed to start"
        kill $BACKEND_PID 2>/dev/null
        return 1
    fi
    
    # Check if frontend is running (port 3000)
    if wait_for_port 3000 30; then
        echo "✅ Frontend started automatically"
    else
        echo "❌ Frontend failed to start automatically"
        kill $BACKEND_PID 2>/dev/null
        return 1
    fi
    
    # Test if the proxy is working
    echo "🌐 Testing proxy functionality..."
    if curl -s -o /dev/null -w "%{http_code}" https://localhost:5001 | grep -q "200\|302"; then
        echo "✅ Backend proxy is working"
    else
        echo "⚠️  Backend proxy test inconclusive (may require HTTPS cert)"
    fi
    
    # Cleanup
    echo "🧹 Cleaning up development test..."
    kill $BACKEND_PID 2>/dev/null
    sleep 5
    
    # Kill any remaining processes on ports 3000 and 5001
    pkill -f "dotnet.*WHO.MALARIA.Web" 2>/dev/null
    pkill -f "vite" 2>/dev/null
    
    echo "✅ Development mode test completed"
    return 0
}

# Function to test production mode
test_production() {
    echo ""
    echo "🏭 Testing Production Mode"
    echo "--------------------------"
    
    # Check if required files exist
    if [ ! -f "WHO.MALARIA.Web/malaria-client/build.sh" ]; then
        echo "❌ build.sh script not found"
        return 1
    fi
    
    echo "✅ Required files exist"
    
    # Remove existing build to test auto-build
    if [ -d "WHO.MALARIA.Web/malaria-client/build" ]; then
        echo "🗑️  Removing existing build to test auto-build..."
        rm -rf WHO.MALARIA.Web/malaria-client/build
    fi
    
    # Start the application in production mode
    echo "🚀 Starting application in production mode..."
    cd WHO.MALARIA.Web
    ASPNETCORE_ENVIRONMENT=Production dotnet run &
    BACKEND_PID=$!
    cd ..
    
    # Wait for backend to start and build frontend
    echo "⏳ Waiting for build and startup..."
    sleep 20
    
    # Check if backend is running
    if wait_for_port 5001 60; then
        echo "✅ Backend started successfully"
    else
        echo "❌ Backend failed to start"
        kill $BACKEND_PID 2>/dev/null
        return 1
    fi
    
    # Check if build was created
    if [ -d "WHO.MALARIA.Web/malaria-client/build" ]; then
        echo "✅ Frontend was built automatically"
    else
        echo "❌ Frontend build was not created"
        kill $BACKEND_PID 2>/dev/null
        return 1
    fi
    
    # Test if the application is serving static files
    echo "🌐 Testing static file serving..."
    if curl -s -o /dev/null -w "%{http_code}" https://localhost:5001 | grep -q "200\|302"; then
        echo "✅ Static file serving is working"
    else
        echo "⚠️  Static file serving test inconclusive (may require HTTPS cert)"
    fi
    
    # Cleanup
    echo "🧹 Cleaning up production test..."
    kill $BACKEND_PID 2>/dev/null
    sleep 5
    
    pkill -f "dotnet.*WHO.MALARIA.Web" 2>/dev/null
    
    echo "✅ Production mode test completed"
    return 0
}

# Main test execution
main() {
    echo "📋 Prerequisites check..."
    
    # Check if we're in the right directory
    if [ ! -f "WHO.MALARIA.Web/WHO.MALARIA.Web.csproj" ]; then
        echo "❌ Please run this script from the project root directory"
        exit 1
    fi
    
    # Check if dotnet is available
    if ! command -v dotnet &> /dev/null; then
        echo "❌ .NET SDK not found"
        exit 1
    fi
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js not found"
        exit 1
    fi
    
    echo "✅ Prerequisites satisfied"
    
    # Run tests
    if test_development; then
        echo "✅ Development mode test PASSED"
    else
        echo "❌ Development mode test FAILED"
        exit 1
    fi
    
    if test_production; then
        echo "✅ Production mode test PASSED"
    else
        echo "❌ Production mode test FAILED"
        exit 1
    fi
    
    echo ""
    echo "🎉 All tests PASSED!"
    echo "Frontend auto-start functionality is working correctly."
}

# Run the tests
main "$@"
