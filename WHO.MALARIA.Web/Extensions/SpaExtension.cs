﻿using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WHO.MALARIA.Domain.Constants;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class SpaExtension
    {
        private static readonly string DevScriptPath = "dev.sh";
        private static readonly string BuildScriptPath = "build.sh";
        private static readonly string ViteDevServerUrl = "http://localhost:3000";

        internal static IServiceCollection AddSpaStaticFiles(this IServiceCollection services)
        {
            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = Constants.Startup.SpaBuildFolderName;
            });

            return services;
        }

        internal static IApplicationBuilder UseSpa(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            var logger = app.ApplicationServices.GetRequiredService<ILogger<Program>>();

            app.UseSpa(spa =>
            {
                spa.Options.SourcePath = Path.Join(env.ContentRootPath, Constants.Startup.SpaProjectName);

                if (env.IsDevelopment())
                {
                    // Configure for Vite development server
                    spa.Options.StartupTimeout = TimeSpan.FromSeconds(120);

                    // Automatically start the frontend development server
                    spa.UseViteDevelopmentServer(logger);
                }
                else
                {
                    // Ensure frontend is built for production
                    EnsureFrontendBuilt(spa.Options.SourcePath, logger);
                }
            });

            return app;
        }

        private static void UseViteDevelopmentServer(this ISpaBuilder spa, ILogger logger)
        {
            spa.UseProxyToSpaDevelopmentServer(() =>
            {
                // Start the Vite development server
                var spaPath = spa.Options.SourcePath;
                var devScriptFullPath = Path.Combine(spaPath, DevScriptPath);

                if (!File.Exists(devScriptFullPath))
                {
                    logger.LogWarning("Development script not found at {DevScriptPath}. Frontend will not start automatically.", devScriptFullPath);
                    return Task.FromResult(new Uri(ViteDevServerUrl));
                }

                logger.LogInformation("Starting Vite development server...");

                var startInfo = new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = devScriptFullPath,
                    WorkingDirectory = spaPath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                // Handle Windows differently
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    startInfo.FileName = "cmd.exe";
                    startInfo.Arguments = $"/c bash {devScriptFullPath}";
                }

                try
                {
                    var process = Process.Start(startInfo);
                    if (process != null)
                    {
                        logger.LogInformation("Vite development server started with PID {ProcessId}", process.Id);

                        // Wait for the server to be ready
                        WaitForViteServer(logger);
                    }
                    else
                    {
                        logger.LogError("Failed to start Vite development server");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error starting Vite development server");
                }

                return Task.FromResult(new Uri(ViteDevServerUrl));
            });
        }

        private static void WaitForViteServer(ILogger logger)
        {
            var maxAttempts = 30; // 30 seconds
            var attempt = 0;

            logger.LogInformation("Waiting for Vite development server to be ready...");

            while (attempt < maxAttempts)
            {
                try
                {
                    using var client = new System.Net.Http.HttpClient();
                    client.Timeout = TimeSpan.FromSeconds(2);
                    var response = client.GetAsync(ViteDevServerUrl).Result;

                    if (response.IsSuccessStatusCode)
                    {
                        logger.LogInformation("Vite development server is ready at {Url}", ViteDevServerUrl);
                        return;
                    }
                }
                catch
                {
                    // Server not ready yet
                }

                Thread.Sleep(1000);
                attempt++;
            }

            logger.LogWarning("Vite development server did not respond within {MaxAttempts} seconds", maxAttempts);
        }

        private static void EnsureFrontendBuilt(string spaPath, ILogger logger)
        {
            var buildPath = Path.Combine(spaPath, "build");
            var buildScriptFullPath = Path.Combine(spaPath, BuildScriptPath);

            // Check if build exists and is recent
            if (Directory.Exists(buildPath))
            {
                var buildTime = Directory.GetLastWriteTime(buildPath);
                var sourceTime = GetLatestSourceFileTime(Path.Combine(spaPath, "src"));

                if (buildTime > sourceTime)
                {
                    logger.LogInformation("Frontend build is up to date");
                    return;
                }
            }

            if (!File.Exists(buildScriptFullPath))
            {
                logger.LogWarning("Build script not found at {BuildScriptPath}. Please build frontend manually.", buildScriptFullPath);
                return;
            }

            logger.LogInformation("Building frontend for production...");

            var startInfo = new ProcessStartInfo
            {
                FileName = "/bin/bash",
                Arguments = buildScriptFullPath,
                WorkingDirectory = spaPath,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            // Handle Windows differently
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                startInfo.FileName = "cmd.exe";
                startInfo.Arguments = $"/c bash {buildScriptFullPath}";
            }

            try
            {
                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        logger.LogInformation("Frontend build completed successfully");
                    }
                    else
                    {
                        logger.LogError("Frontend build failed with exit code {ExitCode}", process.ExitCode);
                    }
                }
                else
                {
                    logger.LogError("Failed to start frontend build process");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error building frontend");
            }
        }

        private static DateTime GetLatestSourceFileTime(string sourcePath)
        {
            if (!Directory.Exists(sourcePath))
                return DateTime.MinValue;

            var latestTime = DateTime.MinValue;

            try
            {
                foreach (var file in Directory.GetFiles(sourcePath, "*", SearchOption.AllDirectories))
                {
                    var fileTime = File.GetLastWriteTime(file);
                    if (fileTime > latestTime)
                        latestTime = fileTime;
                }
            }
            catch
            {
                // Ignore errors when checking file times
            }

            return latestTime;
        }
    }
}
