# Frontend Auto-Start Configuration

This document describes the automatic frontend startup functionality that has been implemented for the Malaria Surveillance Toolkit.

## Overview

The backend application now automatically starts the frontend when running `dotnet run`, eliminating the need to manually start the frontend in a separate terminal.

## How It Works

### Development Environment
When running in **Development** mode (`ASPNETCORE_ENVIRONMENT=Development`):

1. **Automatic Frontend Startup**: The backend automatically executes the `dev.sh` script in the `malaria-client` directory
2. **Process Management**: Starts the Vite development server as a background process
3. **Health Checking**: Waits for the Vite server to be ready before proceeding
4. **Proxy Configuration**: Proxies frontend requests from the backend to the Vite dev server at `http://localhost:3000`

### Production Environment
When running in **Production** mode:

1. **Build Check**: Automatically checks if the frontend build is up-to-date
2. **Auto-Build**: If the build is missing or outdated, automatically runs the `build.sh` script
3. **Static File Serving**: Serves the built frontend files directly from the backend

## Configuration

### Required Files
The following files must exist in the `malaria-client` directory:

- `dev.sh` - Development script that starts the Vite dev server
- `build.sh` - Production build script that builds the frontend
- `vite.config.js` - Vite configuration (must use port 3000)

### Environment Variables
- `ASPNETCORE_ENVIRONMENT` - Set to `Development` for auto-start, `Production` for auto-build

## Usage

### Development
```bash
# Simply run the backend - frontend will start automatically
dotnet run --project WHO.MALARIA.Web

# Or use the development environment explicitly
ASPNETCORE_ENVIRONMENT=Development dotnet run --project WHO.MALARIA.Web
```

### Production
```bash
# Set production environment - frontend will be built automatically if needed
ASPNETCORE_ENVIRONMENT=Production dotnet run --project WHO.MALARIA.Web
```

## URLs

### Development Mode
- **Backend API**: https://localhost:5001
- **Frontend (Vite Dev Server)**: http://localhost:3000
- **Integrated Application**: https://localhost:5001 (proxies to frontend)

### Production Mode
- **Application**: https://localhost:5001 (serves built frontend)

## Logging

The system provides detailed logging for frontend operations:

- **Info**: Frontend startup/build progress
- **Warning**: Missing scripts or configuration issues
- **Error**: Failed startup or build processes

## Troubleshooting

### Frontend Doesn't Start
1. Check that `dev.sh` exists and is executable
2. Verify Node.js version (requires 18+, recommended 22.15.1)
3. Check the application logs for error messages
4. Ensure port 3000 is available

### Build Fails in Production
1. Check that `build.sh` exists and is executable
2. Verify all dependencies are installed (`npm install`)
3. Check disk space and permissions
4. Review build logs for specific errors

### Port Conflicts
If port 3000 is already in use:
1. Stop other processes using port 3000
2. Or modify `vite.config.js` and update `ViteDevServerUrl` in `SpaExtension.cs`

## Technical Details

### Implementation
- **File**: `WHO.MALARIA.Web/Extensions/SpaExtension.cs`
- **Method**: `UseViteDevelopmentServer()` for development
- **Method**: `EnsureFrontendBuilt()` for production

### Process Management
- Uses `ProcessStartInfo` to execute shell scripts
- Cross-platform support (Linux/macOS/Windows)
- Proper process cleanup and error handling

### Health Checking
- Polls the Vite dev server until it responds
- 30-second timeout with 1-second intervals
- Graceful fallback if server doesn't respond

## Benefits

1. **Simplified Development**: Single command starts both frontend and backend
2. **Automatic Production Builds**: No manual build step required
3. **Cross-Platform**: Works on Windows, macOS, and Linux
4. **Robust Error Handling**: Comprehensive logging and fallback mechanisms
5. **Performance**: Only builds when source files have changed
